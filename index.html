<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时关机程序</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="index.css">
</head>
<body class="bg-light">
    <div class="container py-5">
        <h1 class="text-center mb-5 text-primary fw-bold">定时关机设置</h1>
        
        <div class="card shadow-lg border-0 mb-4">
            <div class="card-header d-flex justify-content-between align-items-center bg-white border-0 py-3">
                <h2 class="h5 mb-0 text-secondary">已设置计划</h2>
                <button id="openModalBtn" class="btn btn-primary d-flex align-items-center">
                    <svg class="me-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" width="1.2em" height="1.2em"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                    添加新计划
                </button>
            </div>
            
            <div class="card-body">
                <div class="schedule-list" id="scheduleList"></div>
                <span id="debugInfo" class="d-block text-center mt-4 text-muted small user-select-all"></span>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addScheduleModal" tabindex="-1" aria-labelledby="addScheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header">
                    <h5 class="modal-title" id="addScheduleModalLabel">添加定时关机计划</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="timeInput" class="form-label fw-bold">选择时间:</label>
                        <input type="time" id="timeInput" step="1" class="form-control form-control-lg">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">选择星期:</label>
                        <div class="days-select d-grid gap-2">
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="1" class="form-check-input me-1"> 周一
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="2" class="form-check-input me-1"> 周二
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="3" class="form-check-input me-1"> 周三
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="4" class="form-check-input me-1"> 周四
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="5" class="form-check-input me-1"> 周五
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="6" class="form-check-input me-1"> 周六
                            </label>
                            <label class="day-checkbox form-check-label border rounded p-2 text-center bg-light">
                                <input type="checkbox" value="7" class="form-check-input me-1"> 周日
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelAddBtn">取消</button>
                    <button type="button" class="btn btn-primary" id="addScheduleBtn">确认添加</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="infoModal" tabindex="-1" aria-labelledby="infoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content border-0 shadow-sm">
                <div class="modal-header border-0 pb-0">
                    <h5 class="modal-title" id="infoModalLabel">提示</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pt-0 text-center">
                    <p id="infoModalBody" class="mb-0"></p>
                </div>
                <div class="modal-footer border-0 pt-0 justify-content-center">
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="script.js"></script>
</body>
</html>