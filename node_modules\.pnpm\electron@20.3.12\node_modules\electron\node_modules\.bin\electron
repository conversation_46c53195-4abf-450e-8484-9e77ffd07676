#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Work/electron/node_modules/.pnpm/electron@20.3.12/node_modules/electron/node_modules:/mnt/c/Work/electron/node_modules/.pnpm/electron@20.3.12/node_modules:/mnt/c/Work/electron/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Work/electron/node_modules/.pnpm/electron@20.3.12/node_modules/electron/node_modules:/mnt/c/Work/electron/node_modules/.pnpm/electron@20.3.12/node_modules:/mnt/c/Work/electron/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
