{"main": "index.js", "types": "electron.d.ts", "bin": {"electron": "cli.js"}, "scripts": {"postinstall": "node install.js"}, "dependencies": {"@electron/get": "^1.14.1", "@types/node": "^16.11.26", "extract-zip": "^2.0.1"}, "engines": {"node": ">= 10.17.0"}, "name": "electron", "repository": "https://github.com/electron/electron", "description": "Build cross platform desktop apps with JavaScript, HTML, and CSS", "license": "MIT", "author": "Electron Community", "keywords": ["electron"], "version": "20.3.12"}