// countdown.js
document.addEventListener('DOMContentLoaded', () => {
    const countdownElement = document.getElementById('countdown');
    const cancelBtn = document.getElementById('cancel-shutdown');
    let timeLeft = 10; // 10秒倒计时

    const timerId = setInterval(() => {
        timeLeft--;
        countdownElement.textContent = timeLeft;
        if (timeLeft <= 0) {
            clearInterval(timerId);
            // 倒计时结束，调用主进程的关机命令
            window.electronAPI.executeShutdown();
        }
    }, 1000);

    // 点击取消按钮
    cancelBtn.addEventListener('click', () => {
        clearInterval(timerId);
        // 通知主进程关闭此窗口
        window.electronAPI.closeCountdownWindow();
    });
});