{"name": "@szmarczak/http-timer", "version": "1.1.2", "description": "Timings for HTTP requests", "main": "source", "engines": {"node": ">=6"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["source"], "keywords": ["http", "https", "timer", "timings"], "repository": {"type": "git", "url": "git+https://github.com/szmarczak/http-timer.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/szmarczak/http-timer/issues"}, "homepage": "https://github.com/szmarczak/http-timer#readme", "xo": {"rules": {"unicorn/filename-case": "camelCase"}}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.2", "p-event": "^2.1.0", "nyc": "^12.0.2", "xo": "^0.22.0"}, "dependencies": {"defer-to-connect": "^1.0.1"}}