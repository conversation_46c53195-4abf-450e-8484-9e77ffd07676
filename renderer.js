// renderer.js
document.addEventListener('DOMContentLoaded', () => {
    const schedulesList = document.getElementById('schedules-list');
    const addBtn = document.getElementById('add-schedule');
    const saveBtn = document.getElementById('save-schedules');

    // 添加一个新的时间设置行
    const addScheduleRow = (timeValue = '', selectedDays = []) => {
        const itemId = `schedule-${Date.now()}`;
        const item = document.createElement('div');
        item.classList.add('schedule-item');
        item.id = itemId;
        item.innerHTML = `
            <input type="time" class="time" step="1" value="${timeValue}">
            <span class="days">
                星期:
                <input type="checkbox" value="1" ${selectedDays.includes(1) ? 'checked' : ''}>一
                <input type="checkbox" value="2" ${selectedDays.includes(2) ? 'checked' : ''}>二
                <input type="checkbox" value="3" ${selectedDays.includes(3) ? 'checked' : ''}>三
                <input type="checkbox" value="4" ${selectedDays.includes(4) ? 'checked' : ''}>四
                <input type="checkbox" value="5" ${selectedDays.includes(5) ? 'checked' : ''}>五
                <input type="checkbox" value="6" ${selectedDays.includes(6) ? 'checked' : ''}>六
                <input type="checkbox" value="7" ${selectedDays.includes(7) ? 'checked' : ''}>日
            </span>
            <button onclick="document.getElementById('${itemId}').remove()">删除</button>
        `;
        schedulesList.appendChild(item);
    };

    // 保存设置并发送到主进程
    const saveSchedules = () => {
        const schedules = [];
        const items = document.querySelectorAll('.schedule-item');
        items.forEach(item => {
            const timeInput = item.querySelector('.time').value;
            if (!timeInput) return; // 如果未设置时间则跳过

            const selectedDays = [];
            item.querySelectorAll('.days input:checked').forEach(day => {
                selectedDays.push(parseInt(day.value));
            });

            if (selectedDays.length > 0) {
                // 解析时间字符串为对象格式
                const [hours, minutes, seconds] = timeInput.split(':');
                schedules.push({
                    time: {
                        hh: parseInt(hours) || 0,
                        mm: parseInt(minutes) || 0,
                        ss: parseInt(seconds) || 0
                    },
                    days: selectedDays
                });
            }
        });
        window.electronAPI.setShutdownSchedules(schedules);
        alert('计划已保存！程序将在后台监听。');
    };

    // 加载已保存的配置
    const loadSavedSchedules = async () => {
        try {
            const savedSchedules = await window.electronAPI.getShutdownSchedules();
            if (savedSchedules && savedSchedules.length > 0) {
                // 清空现有的计划列表
                schedulesList.innerHTML = '';

                // 加载保存的计划
                savedSchedules.forEach(schedule => {
                    // 处理新旧格式兼容
                    let timeValue;
                    if (typeof schedule.time === 'string') {
                        // 旧格式：hh-mm-ss 字符串
                        timeValue = schedule.time.replace(/-/g, ':');
                    } else {
                        // 新格式：{hh: xx, mm: xx, ss: xx} 对象
                        const hh = schedule.time.hh.toString().padStart(2, '0');
                        const mm = schedule.time.mm.toString().padStart(2, '0');
                        const ss = schedule.time.ss.toString().padStart(2, '0');
                        timeValue = `${hh}:${mm}:${ss}`;
                    }
                    addScheduleRow(timeValue, schedule.days);
                });

            } else {
                // 如果没有保存的配置，添加一个空行
                addScheduleRow();
            }
        } catch (error) {
            console.error('failed to load config:', error);
            // 出错时添加一个空行
            addScheduleRow();
        }
    };

    addBtn.addEventListener('click', () => addScheduleRow());
    saveBtn.addEventListener('click', saveSchedules);

    // 加载已保存的配置
    loadSavedSchedules();
});