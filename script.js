// schedules 数组用于存储定时关机计划
let schedules = [];

// 获取 DOM 元素
const addScheduleModalEl = document.getElementById('addScheduleModal');
const addScheduleModal = new bootstrap.Modal(addScheduleModalEl); // 初始化 Bootstrap Modal 实例

const infoModalEl = document.getElementById('infoModal');
const infoModal = new bootstrap.Modal(infoModalEl); // 初始化提示信息模态框
const infoModalBody = document.getElementById('infoModalBody');

const openModalBtn = document.getElementById('openModalBtn');
const addScheduleBtn = document.getElementById('addScheduleBtn');
const timeInput = document.getElementById('timeInput');
const dayCheckboxes = document.querySelectorAll('.day-checkbox input[type="checkbox"]');
const scheduleList = document.getElementById('scheduleList');
const debugInfo = document.getElementById('debugInfo');


/**
 * 页面加载时初始化。
 * 尝试从主进程获取已保存的定时关机配置。
 */
async function initializePage() {
    try {
        // 从主进程获取已保存的配置
        schedules = await window.electronAPI.getShutdownSchedules();
        console.log('加载计划:', schedules);
        updateScheduleList(); // 更新页面显示的计划列表
    } catch (error) {
        console.error('加载计划失败:', error);
        schedules = []; // 如果加载失败，则初始化为空数组
    }
}

/**
 * 显示提示信息模态框。
 * @param {string} message - 要显示的消息。
 */
function showInfoModal(message) {
    infoModalBody.textContent = message;
    infoModal.show();
}

/**
 * 打开添加计划的模态弹窗。
 */
function openAddScheduleModal() {
    // 重置表单，确保每次打开弹窗都是空白状态
    timeInput.value = '';
    dayCheckboxes.forEach(checkbox => checkbox.checked = false);
    addScheduleModal.show(); // 使用 Bootstrap 的 show 方法显示模态框
}

/**
 * 关闭添加计划的模态弹窗。
 */
function closeAddScheduleModal() {
    addScheduleModal.hide(); // 使用 Bootstrap 的 hide 方法隐藏模态框
}

/**
 * 添加新的定时关机计划。
 */
function addSchedule() {
    // 获取模态弹窗中的时间输入和星期选择
    const selectedDays = Array.from(dayCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => parseInt(checkbox.value));

    // 检查时间是否输入且至少选择了一个星期
    if (timeInput.value && selectedDays.length > 0) {
        // 分割时间字符串为小时、分钟、秒
        const [hours, minutes, seconds] = timeInput.value.split(':');

        // 将新计划添加到 schedules 数组
        schedules.push({
            time: {
                hh: parseInt(hours) || 0,
                mm: parseInt(minutes) || 0,
                ss: parseInt(seconds) || 0
            },
            days: selectedDays
        });

        updateScheduleList(); // 更新页面显示的计划列表
        updateMainProcess();   // 将更新后的计划发送给主进程
        closeAddScheduleModal(); // 关闭模态弹窗
    } else {
        showInfoModal('请选择时间和至少一个星期。'); // 使用 Bootstrap 模态框提示
    }
}

/**
 * 删除指定索引的定时关机计划。
 * @param {number} index - 要删除的计划在 schedules 数组中的索引。
 */
function deleteSchedule(index) {
    // 弹出确认对话框
    if (confirm('确定要删除这个计划吗？')) { 
        schedules.splice(index, 1); // 从数组中删除计划
        updateScheduleList();       // 更新页面显示的计划列表
        updateMainProcess();        // 将更新后的计划发送给主进程
    }
}

/**
 * 更新页面上显示的定时关机计划列表。
 */
function updateScheduleList() {
    scheduleList.innerHTML = ''; // 清空当前列表

    // 如果没有计划，显示提示信息
    if (schedules.length === 0) {
        scheduleList.innerHTML = '<p class="text-muted text-center pt-3">暂无定时关机计划</p>';
        return;
    }

    // 遍历所有计划并创建对应的 HTML 元素
    schedules.forEach((schedule, index) => {
        const item = document.createElement('div');
        item.className = 'schedule-item'; // 添加样式类

        const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']; // 修改为中文表示
        // 将数字星期转换为中文名称
        const dayNames = schedule.days.map(d => days[d - 1]).join(', ');

        let timeDisplay;
        // 兼容处理新旧时间格式
        if (typeof schedule.time === 'string') {
            // 旧格式：hh-mm-ss 字符串
            timeDisplay = schedule.time.replace(/-/g, ':');
        } else {
            // 新格式：{hh: xx, mm: xx, ss: xx} 对象，格式化为 hh:mm:ss
            const hh = schedule.time.hh.toString().padStart(2, '0');
            const mm = schedule.time.mm.toString().padStart(2, '0');
            const ss = schedule.time.ss.toString().padStart(2, '0');
            timeDisplay = `${hh}:${mm}:${ss}`;
        }

        // 设置计划项的 HTML 内容
        item.innerHTML = `
            <span>时间: ${timeDisplay} 星期: ${dayNames}</span>
            <button class="btn btn-danger btn-sm" onclick="deleteSchedule(${index})">删除</button>
        `;

        scheduleList.appendChild(item); // 将计划项添加到列表中
    });
}

/**
 * 将当前的定时关机计划发送给主进程进行保存。
 */
function updateMainProcess() {
    try {
        window.electronAPI.setShutdownSchedules(schedules);
        console.log('配置已发送至主进程:', schedules);
    } catch (error) {
        console.error('更新配置失败:', error);
    }
}

// 获取浏览器 UA, 作为调试信息
debugInfo.innerText = navigator.userAgent;

// 绑定事件监听器
openModalBtn.addEventListener('click', openAddScheduleModal); // 点击按钮打开弹窗
addScheduleBtn.addEventListener('click', addSchedule); // 点击确认添加按钮添加计划

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializePage);