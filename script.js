import { ScheduleService } from './src/services/ScheduleService.js';
import { UIManager } from './src/components/UIManager.js';
import { ModalManager } from './src/components/ModalManager.js';
import { parseTimeInput } from './src/utils/timeUtils.js';

const scheduleService = new ScheduleService();
const uiManager = new UIManager();
const modalManager = new ModalManager();

async function initializePage() {
    try {
        const schedules = await scheduleService.loadSchedules();
        uiManager.updateScheduleList(schedules);
    } catch (error) {
        console.error('加载计划失败:', error);
    }
}

function addSchedule() {
    const timeInput = document.getElementById('timeInput');
    const dayCheckboxes = document.querySelectorAll('.day-checkbox input[type="checkbox"]');

    const selectedDays = Array.from(dayCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => parseInt(checkbox.value));

    if (timeInput.value && selectedDays.length > 0) {
        const newSchedule = {
            time: parseTimeInput(timeInput.value),
            days: selectedDays
        };

        scheduleService.addSchedule(newSchedule);
        uiManager.updateScheduleList(scheduleService.getSchedules());
        modalManager.closeAddScheduleModal();
    } else {
        modalManager.showInfoModal('请选择时间和至少一个星期。');
    }
}

function deleteSchedule(index) {
    if (confirm('确定要删除这个计划吗？')) {
        scheduleService.deleteSchedule(index);
        uiManager.updateScheduleList(scheduleService.getSchedules());
    }
}

window.deleteSchedule = deleteSchedule;

document.getElementById('openModalBtn').addEventListener('click', () => modalManager.openAddScheduleModal());
document.getElementById('addScheduleBtn').addEventListener('click', addSchedule);
document.addEventListener('DOMContentLoaded', initializePage);

document.getElementById('debugInfo').innerText = navigator.userAgent;