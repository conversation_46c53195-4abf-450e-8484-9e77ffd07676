@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Work\electron\node_modules\.pnpm\electron@20.3.12\node_modules\electron\node_modules;C:\Work\electron\node_modules\.pnpm\electron@20.3.12\node_modules;C:\Work\electron\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Work\electron\node_modules\.pnpm\electron@20.3.12\node_modules\electron\node_modules;C:\Work\electron\node_modules\.pnpm\electron@20.3.12\node_modules;C:\Work\electron\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\cli.js" %*
)
