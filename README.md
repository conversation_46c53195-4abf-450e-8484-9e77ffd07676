# Electron 自动关机程序

这是一个基于 Electron 的自动关机程序，支持配置持久化功能。

## 功能特性

- ✅ 设置多个关机时间计划
- ✅ 选择特定的星期几执行
- ✅ 系统托盘运行，支持后台监听
- ✅ 全屏倒计时提醒
- ✅ **配置持久化** - 自动保存和加载配置

## 配置持久化功能

### 自动保存
- 当用户点击"保存计划"按钮时，配置会自动保存到 `config.json` 文件
- 配置文件保存在程序根目录下
- 包含关机计划和最后更新时间

### 自动加载
- 程序启动时会自动检查并加载 `config.json` 文件
- 如果配置文件存在，会恢复之前保存的所有关机计划
- 如果有计划存在，会自动启动定时器开始监听
- 前端界面会显示已保存的配置

### 配置文件格式

```json
{
  "shutdownSchedules": [
    {
      "time": "22-30-00",
      "days": [1, 2, 3, 4, 5]
    },
    {
      "time": "23-00-00", 
      "days": [6, 7]
    }
  ],
  "lastUpdated": "2025-06-25T10:30:00.000Z"
}
```

- `time`: 关机时间，格式为 "HH-MM-SS"
- `days`: 星期几数组，1-7 分别代表周一到周日
- `lastUpdated`: 最后更新时间

## 使用方法

1. 启动程序：`npm start`
2. 在界面中设置关机时间和星期
3. 点击"保存计划"按钮
4. 配置会自动保存到 `config.json`
5. 下次启动时会自动加载配置

## 技术实现

- 使用 Node.js `fs` 模块进行文件读写
- 通过 IPC 通信在主进程和渲染进程间传递配置数据
- 启动时自动加载配置并启动定时器
- 保存时实时更新配置文件

## 注意事项

- 配置文件位于程序根目录，请勿手动删除
- 如果配置文件损坏，程序会使用默认配置
- 程序会在控制台输出配置加载和保存的日志信息
