/* 通用样式 */
body {
    font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    color: #343a40;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
}

/* 星期选择样式 */
.days-select {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
}

.day-checkbox {
    cursor: pointer;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

.day-checkbox:hover {
    background-color: #e9ecef !important; /* 鼠标悬停效果 */
    border-color: #dee2e6 !important;
}

.day-checkbox input[type="checkbox"] {
    /* 隐藏原生 checkbox */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    display: inline-block; /* 保持在行内 */
    width: 18px;
    height: 18px;
    border: 2px solid #ced4da; /* 默认边框颜色 */
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    vertical-align: middle; /* 垂直居中对齐 */
    margin-top: -3px; /* 微调位置 */
    transition: background-color 0.2s ease, border-color 0.2s ease;
}

.day-checkbox input[type="checkbox"]:checked {
    background-color: #0d6efd; /* Bootstrap primary 蓝色 */
    border-color: #0d6efd;
}

.day-checkbox input[type="checkbox"]:checked::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 计划列表项 */
.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem; /* 调整内边距 */
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: .5rem; /* 调整圆角 */
    margin-bottom: .75rem; /* 调整间距 */
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); /* 轻微阴影 */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.schedule-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.schedule-item span {
    font-size: 1.05em;
    color: #495057;
}

.schedule-list p {
    text-align: center;
    color: #adb5bd;
    padding: 1.5rem;
    font-style: italic;
}

/* 调试信息 */
.debug-info {
    font-size: 0.75em;
    color: #ced4da;
}

/* 响应式调整 */
@media (max-width: 576px) { /* Bootstrap sm breakpoint */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}